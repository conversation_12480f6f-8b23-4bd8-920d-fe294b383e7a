#!/usr/bin/env python3
"""
Script de teste para validar as otimizações implementadas no bot de trading.
"""

import sys
import time
import asyncio
from unittest.mock import Mock, patch

# Adicionar o diretório atual ao path
sys.path.append('.')

def test_imports():
    """Testar se todas as importações funcionam corretamente."""
    try:
        from 999_base_trail_wma_optuna import (
            AdvancedCache,
            VolatilityCalculator,
            PositionSizer,
            TrailingStopCalculator,
            OptimizedOptuna,
            CacheManager,
            performance_monitor
        )
        print("✅ Todas as importações funcionaram corretamente")
        return True
    except ImportError as e:
        print(f"❌ Erro de importação: {e}")
        return False

def test_advanced_cache():
    """Testar funcionalidade do AdvancedCache."""
    try:
        from 999_base_trail_wma_optuna import AdvancedCache
        
        cache = AdvancedCache(default_ttl=2, max_size=3)
        
        # Testar set/get
        cache.set("test1", "value1")
        assert cache.get("test1") == "value1"
        print("✅ Cache set/get funcionando")
        
        # Testar TTL
        time.sleep(3)
        assert cache.get("test1") is None
        print("✅ TTL funcionando corretamente")
        
        # Testar LRU eviction
        cache.set("key1", "val1")
        cache.set("key2", "val2")
        cache.set("key3", "val3")
        cache.set("key4", "val4")  # Deve remover key1
        
        assert cache.get("key1") is None
        assert cache.get("key4") == "val4"
        print("✅ LRU eviction funcionando")
        
        return True
    except Exception as e:
        print(f"❌ Erro no teste de cache: {e}")
        return False

def test_volatility_calculator():
    """Testar VolatilityCalculator com mock."""
    try:
        from 999_base_trail_wma_optuna import VolatilityCalculator
        from core.config import BotConfig
        
        # Mock do indicator
        mock_indicator = Mock()
        mock_indicator.calculate_atr.return_value = 0.05
        mock_indicator._fetch_ohlcv_data.return_value = None  # Simular dados insuficientes
        
        calc = VolatilityCalculator(lookback_periods=20, cache_ttl=60)
        
        # Testar com dados insuficientes
        result = calc.calculate_volatility_metrics("BTC/USDC", mock_indicator)
        assert result is None
        print("✅ VolatilityCalculator lida corretamente com dados insuficientes")
        
        return True
    except Exception as e:
        print(f"❌ Erro no teste de volatilidade: {e}")
        return False

def test_position_sizer():
    """Testar PositionSizer."""
    try:
        from 999_base_trail_wma_optuna import PositionSizer
        from core.config import BotConfig
        
        config = BotConfig()
        config.POSITION_SIZE_BASE = 0.1
        config.ENABLE_DYNAMIC_POSITION_SIZING = True
        config.VOLATILITY_THRESHOLD_HIGH = 1.2
        config.VOLATILITY_THRESHOLD_LOW = 0.8
        config.POSITION_SIZE_MIN = 0.05
        config.POSITION_SIZE_MAX = 0.15
        
        sizer = PositionSizer(config)
        
        # Testar sem métricas de volatilidade
        size = sizer.calculate_position_size("BTC/USDC", 1000.0)
        assert size == 0.1
        print("✅ PositionSizer funcionando sem volatilidade")
        
        # Testar com alta volatilidade
        volatility_metrics = {"volatility_ratio": 1.5}
        size = sizer.calculate_position_size("BTC/USDC", 1000.0, volatility_metrics)
        assert size == 0.05
        print("✅ PositionSizer reduz posição com alta volatilidade")
        
        return True
    except Exception as e:
        print(f"❌ Erro no teste de position sizer: {e}")
        return False

def test_performance_monitor():
    """Testar decorator de performance."""
    try:
        from 999_base_trail_wma_optuna import performance_monitor
        
        @performance_monitor
        def test_function():
            time.sleep(0.1)  # Simular processamento
            return "success"
        
        result = test_function()
        assert result == "success"
        print("✅ Performance monitor funcionando")
        
        return True
    except Exception as e:
        print(f"❌ Erro no teste de performance monitor: {e}")
        return False

async def test_cache_manager():
    """Testar CacheManager."""
    try:
        from 999_base_trail_wma_optuna import CacheManager, AdvancedCache
        
        manager = CacheManager()
        cache = AdvancedCache(default_ttl=1)
        manager.register_cache(cache)
        
        # Adicionar dados expirados
        cache.set("expired", "value")
        time.sleep(2)
        
        # Testar limpeza manual
        cleaned = cache.clear_expired()
        assert cleaned == 1
        print("✅ CacheManager limpeza funcionando")
        
        return True
    except Exception as e:
        print(f"❌ Erro no teste de cache manager: {e}")
        return False

async def main():
    """Executar todos os testes."""
    print("🧪 Iniciando testes das otimizações...")
    print("=" * 50)
    
    tests = [
        ("Importações", test_imports),
        ("AdvancedCache", test_advanced_cache),
        ("VolatilityCalculator", test_volatility_calculator),
        ("PositionSizer", test_position_sizer),
        ("Performance Monitor", test_performance_monitor),
        ("CacheManager", lambda: asyncio.run(test_cache_manager())),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n🔍 Testando {test_name}...")
        try:
            if asyncio.iscoroutinefunction(test_func):
                result = await test_func()
            else:
                result = test_func()
            
            if result:
                passed += 1
                print(f"✅ {test_name}: PASSOU")
            else:
                print(f"❌ {test_name}: FALHOU")
        except Exception as e:
            print(f"❌ {test_name}: ERRO - {e}")
    
    print("\n" + "=" * 50)
    print(f"📊 Resultados: {passed}/{total} testes passaram")
    
    if passed == total:
        print("🎉 Todas as otimizações estão funcionando corretamente!")
        return True
    else:
        print("⚠️ Algumas otimizações precisam de ajustes.")
        return False

if __name__ == "__main__":
    asyncio.run(main())
