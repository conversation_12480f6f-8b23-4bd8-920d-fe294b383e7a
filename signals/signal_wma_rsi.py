"""
Classe para verificar sinais de entrada baseados em WMA e RSI.
"""

from typing import Dict, Optional
from utils.logger import TradingLogger
from utils.error_handler import ErrorHandler
from indicators.indicators import TechnicalIndicator


class SinaisWmaRsi:
    """
    Classe para verificar sinais de entrada baseados em WMA e RSI.
    """

    def __init__(self, indicator: TechnicalIndicator, client):
        self.indicator = indicator
        self.client = client
        self.logger = TradingLogger.get_logger(__name__)
        self.error_handler = ErrorHandler(logger_name=__name__)

    def check_entry_signal(
        self,
        symbol: str,
        timeframe: str = None,
        tickers: Optional[Dict] = None,
    ) -> bool:
        """
        Sinal de compra: RSI(14) > 50 e close > WMA(50)
        """
        if timeframe is None:
            timeframe = self.client.config.TIMEFRAME
        try:
            if tickers and symbol in tickers:
                ticker = tickers[symbol]
            else:
                ticker = self.client.get_ticker(symbol)
            if not ticker or "last" not in ticker:
                self.logger.error(f"Sem dados de ticker para {symbol}")
                return False

            price = ticker["last"]
            rsi = self.indicator.calculate_rsi(
                symbol, timeframe, period=self.client.config.RSI_PERIOD
            )
            wma_period = getattr(self.client.config, 'WMA_PERIOD', 84)
            wma = self.indicator.calculate_wma(
                symbol, timeframe, period=wma_period
            )

            if rsi is None or wma is None:
                self.logger.debug(f"Dados de indicador ausentes para {symbol}")
                return False

            if rsi > 50 and price > wma:
                # self.logger.info(f"Sinal de entrada detectado para {symbol}")
                return True

            return False

        except Exception as exc:
            self.logger.error(f"Erro ao verificar sinal para {symbol}: {exc}")
            return False

    def print_signal(
        self,
        symbol: str,
        timeframe: str = None,
        tickers: Optional[Dict] = None,
    ) -> None:
        """
        Imprime o sinal de trading: BUY ou HOLD, usando WMA 50 e RSI 14.
        """
        if timeframe is None:
            timeframe = self.client.config.TIMEFRAME
        try:
            if tickers and symbol in tickers:
                ticker = tickers[symbol]
            else:
                ticker = self.client.get_ticker(symbol)
            if not ticker or "last" not in ticker:
                print(f"\033[91m • {symbol}: Dados de preço indisponíveis\033[0m")
                print()
                return

            price = ticker["last"]
            rsi = self.indicator.calculate_rsi(
                symbol, timeframe, period=self.client.config.RSI_PERIOD
            )
            wma_period = getattr(self.client.config, 'WMA_PERIOD', 84)
            wma = self.indicator.calculate_wma(
                symbol, timeframe, period=wma_period
            )

            if rsi is None or wma is None:
                print(f"\033[91m • {symbol}: Indicadores indisponíveis\033[0m")
                print()
                return

            if rsi > 50 and price > wma:
                signal = "BUY"
                color = "\033[92m"
            else:
                signal = "HOLD"
                color = "\033[93m"

            print()
            print(f"{color} • {symbol}: SINAL = {signal}\033[0m")
            print(f"   Preço: ${price:.2f}")
            # print(f"   RSI: {rsi:.2f}")
            # print(f"   WMA: ${wma:.2f}")
            print()

        except Exception as exc:
            self.logger.error(f"Erro ao imprimir sinal para {symbol}: {exc}")
            print(f"\033[91m • {symbol}: Erro ao processar sinal\033[0m")
            print()
