"""
Bot de trading automatizado para OKX Exchange utilizando estratégia Trailing Stop com WMA.
"""

import warnings
warnings.filterwarnings("ignore", message="pkg_resources is deprecated as an API")

import asyncio
import os
import numpy as np
import json
import ccxt
import talib
import pandas as pd
import uuid
import time
import sqlite3
import optuna
import threading
import random
from collections import deque
from dotenv import load_dotenv
from typing import Dict, List, Optional, Tuple, Any
from functools import wraps

# Importações dos módulos compartilhados
from core.config import BotConfig, OrderCache
from core.base_bot import TradingBotBase, initialize_bot_components, send_startup_notification
from core.okx_client import OKXClient
from indicators.indicators import TechnicalIndicator
from utils.logger import TradingLogger
from utils.check_orders import check_trailing_stop_orders, check_closed_trailing_stop_orders
from utils.order_validator import OrderValidator
from utils.exceptions import ConfigurationError, ExchangeConnectionError
from utils.database import TradingDatabase
from signals.signal_wma_rsi import SinaisWmaRsi

load_dotenv()


class AdvancedCache:
    """Sistema de cache avançado com TTL e gestão inteligente de memória."""

    def __init__(self, default_ttl: int = 300, max_size: int = 1000):
        self.default_ttl = default_ttl
        self.max_size = max_size
        self.cache = {}
        self.timestamps = {}
        self.access_count = {}
        self.lock = threading.RLock()
        self.logger = TradingLogger.get_logger(__name__)

    def get(self, key: str, ttl: Optional[int] = None) -> Optional[Any]:
        """Obter valor do cache com verificação de TTL."""
        with self.lock:
            if key not in self.cache:
                return None

            current_time = time.time()
            cache_ttl = ttl or self.default_ttl

            if current_time - self.timestamps[key] > cache_ttl:
                self._remove_key(key)
                return None

            self.access_count[key] = self.access_count.get(key, 0) + 1
            return self.cache[key]

    def set(self, key: str, value: Any, ttl: Optional[int] = None) -> None:
        """Armazenar valor no cache com TTL."""
        with self.lock:
            if len(self.cache) >= self.max_size:
                self._evict_lru()

            self.cache[key] = value
            self.timestamps[key] = time.time()
            self.access_count[key] = 1

    def _remove_key(self, key: str) -> None:
        """Remover chave do cache."""
        self.cache.pop(key, None)
        self.timestamps.pop(key, None)
        self.access_count.pop(key, None)

    def _evict_lru(self) -> None:
        """Remover item menos usado recentemente."""
        if not self.cache:
            return

        # Encontrar chave com menor contagem de acesso
        lru_key = min(self.access_count.keys(), key=lambda k: self.access_count[k])
        self._remove_key(lru_key)
        self.logger.debug(f"Cache LRU eviction: removed {lru_key}")

    def clear_expired(self) -> int:
        """Limpar entradas expiradas do cache."""
        with self.lock:
            current_time = time.time()
            expired_keys = []

            for key, timestamp in self.timestamps.items():
                if current_time - timestamp > self.default_ttl:
                    expired_keys.append(key)

            for key in expired_keys:
                self._remove_key(key)

            return len(expired_keys)


class VolatilityCalculator:
    """Calculadora de volatilidade otimizada com cache."""

    def __init__(self, lookback_periods: int = 20, cache_ttl: int = 300):
        self.lookback_periods = lookback_periods
        self.cache = AdvancedCache(default_ttl=cache_ttl)
        self.logger = TradingLogger.get_logger(__name__)

    def calculate_volatility_metrics(self, symbol: str, indicator: TechnicalIndicator,
                                   timeframe: str = "15m") -> Optional[Dict[str, float]]:
        """Calcular métricas de volatilidade com cache."""
        cache_key = f"volatility_{symbol}_{timeframe}_{self.lookback_periods}"

        # Verificar cache primeiro
        cached_result = self.cache.get(cache_key)
        if cached_result is not None:
            return cached_result

        try:
            # Calcular ATR atual
            current_atr = indicator.calculate_atr(symbol, timeframe, period=14)
            if current_atr is None:
                return None

            # Obter dados históricos para média
            # Use _fetch_ohlcv_data instead of fetch_historical_data for limit parameter
            ohlcv_data = indicator._fetch_ohlcv_data(
                symbol, timeframe, limit=self.lookback_periods + 14
            )

            if ohlcv_data is None or len(ohlcv_data) < self.lookback_periods + 14:
                return None

            # Calcular ATR histórico usando dados numpy
            atr_values = []
            for i in range(14, len(ohlcv_data)):
                if i < 14:
                    continue

                high = ohlcv_data[i-14:i, 2]  # High prices
                low = ohlcv_data[i-14:i, 3]   # Low prices
                close = ohlcv_data[i-14:i, 4] # Close prices

                atr_val = talib.ATR(high, low, close, timeperiod=14)[-1]
                if not np.isnan(atr_val):
                    atr_values.append(atr_val)

            if not atr_values:
                return None

            atr_mean = np.mean(atr_values)
            atr_std = np.std(atr_values)
            volatility_ratio = current_atr / atr_mean if atr_mean > 0 else 1.0

            result = {
                'current_atr': current_atr,
                'mean_atr': atr_mean,
                'std_atr': atr_std,
                'volatility_ratio': volatility_ratio,
                'volatility_percentile': self._calculate_percentile(current_atr, atr_values)
            }

            # Armazenar no cache
            self.cache.set(cache_key, result)
            return result

        except Exception as exc:
            self.logger.error(f"Erro ao calcular volatilidade para {symbol}: {exc}")
            return None

    def _calculate_percentile(self, current_value: float, historical_values: List[float]) -> float:
        """Calcular percentil do valor atual em relação aos históricos."""
        if not historical_values:
            return 50.0

        sorted_values = sorted(historical_values)
        position = sum(1 for v in sorted_values if v <= current_value)
        return (position / len(sorted_values)) * 100


class PositionSizer:
    """Calculadora de tamanho de posição otimizada."""

    def __init__(self, config: BotConfig):
        self.config = config
        self.logger = TradingLogger.get_logger(__name__)

    def calculate_position_size(self, symbol: str, available_balance: float,
                              volatility_metrics: Optional[Dict[str, float]] = None) -> float:
        """Calcular tamanho da posição baseado em volatilidade e configuração."""
        base_size = getattr(self.config, 'POSITION_SIZE_BASE', 0.1)

        if not volatility_metrics or not getattr(self.config, 'ENABLE_DYNAMIC_POSITION_SIZING', False):
            return base_size

        volatility_ratio = volatility_metrics.get('volatility_ratio', 1.0)

        # Ajustar tamanho baseado na volatilidade
        if volatility_ratio > getattr(self.config, 'VOLATILITY_THRESHOLD_HIGH', 1.2):
            position_size = getattr(self.config, 'POSITION_SIZE_MIN', 0.05)
            self.logger.info(f"Alta volatilidade para {symbol}: reduzindo posição para {position_size*100:.1f}%")
        elif volatility_ratio < getattr(self.config, 'VOLATILITY_THRESHOLD_LOW', 0.8):
            position_size = getattr(self.config, 'POSITION_SIZE_MAX', 0.15)
            self.logger.info(f"Baixa volatilidade para {symbol}: aumentando posição para {position_size*100:.1f}%")
        else:
            position_size = base_size
            self.logger.info(f"Volatilidade normal para {symbol}: posição padrão {position_size*100:.1f}%")

        return position_size


class TrailingStopCalculator:
    """Calculadora de trailing stop otimizada."""

    def __init__(self, config: BotConfig):
        self.config = config
        self.logger = TradingLogger.get_logger(__name__)

    def calculate_callback_ratio(self, symbol: str, entry_price: float, atr: float,
                                volatility_metrics: Optional[Dict[str, float]] = None) -> float:
        """Calcular callback ratio para trailing stop."""
        # Callback ratio base usando ATR
        callback_ratio_base = (getattr(self.config, 'ATR_MULTIPLIER', 1.75) * atr) / entry_price

        if not volatility_metrics or not getattr(self.config, 'ENABLE_DYNAMIC_TRAILING_RATIO', False):
            return max(0.015, min(0.08, callback_ratio_base))

        volatility_ratio = volatility_metrics.get('volatility_ratio', 1.0)

        # Ajustar callback ratio baseado na volatilidade
        if volatility_ratio > getattr(self.config, 'VOLATILITY_THRESHOLD_HIGH', 1.2):
            callback_ratio = max(
                getattr(self.config, 'CALLBACK_RATIO_MAX', 0.08),
                callback_ratio_base
            )
            self.logger.info(f"Alta volatilidade para {symbol}: callback ratio {callback_ratio*100:.2f}%")
        elif volatility_ratio < getattr(self.config, 'VOLATILITY_THRESHOLD_LOW', 0.8):
            callback_ratio = min(
                getattr(self.config, 'CALLBACK_RATIO_MIN', 0.015),
                callback_ratio_base
            )
            self.logger.info(f"Baixa volatilidade para {symbol}: callback ratio {callback_ratio*100:.2f}%")
        else:
            callback_ratio = max(
                getattr(self.config, 'CALLBACK_RATIO_BASE', 0.03),
                min(callback_ratio_base, getattr(self.config, 'CALLBACK_RATIO_MAX', 0.08))
            )
            self.logger.info(f"Volatilidade normal para {symbol}: callback ratio {callback_ratio*100:.2f}%")

        return callback_ratio


def performance_monitor(func):
    """Decorator para monitorar performance de funções."""
    @wraps(func)
    async def async_wrapper(*args, **kwargs):
        start_time = time.time()
        try:
            result = await func(*args, **kwargs)
            execution_time = time.time() - start_time
            if execution_time > 1.0:  # Log apenas se demorar mais que 1 segundo
                logger = TradingLogger.get_logger(__name__)
                logger.warning(f"Função lenta detectada: {func.__name__} levou {execution_time:.2f}s")
            return result
        except Exception as exc:
            execution_time = time.time() - start_time
            logger = TradingLogger.get_logger(__name__)
            logger.error(f"Erro em {func.__name__} após {execution_time:.2f}s: {exc}")
            raise

    @wraps(func)
    def sync_wrapper(*args, **kwargs):
        start_time = time.time()
        try:
            result = func(*args, **kwargs)
            execution_time = time.time() - start_time
            if execution_time > 1.0:
                logger = TradingLogger.get_logger(__name__)
                logger.warning(f"Função lenta detectada: {func.__name__} levou {execution_time:.2f}s")
            return result
        except Exception as exc:
            execution_time = time.time() - start_time
            logger = TradingLogger.get_logger(__name__)
            logger.error(f"Erro em {func.__name__} após {execution_time:.2f}s: {exc}")
            raise

    return async_wrapper if asyncio.iscoroutinefunction(func) else sync_wrapper


class CacheManager:
    """Gerenciador de cache para limpeza automática e otimização de memória."""

    def __init__(self):
        self.caches = []
        self.cleanup_interval = 300  # 5 minutos
        self.logger = TradingLogger.get_logger(__name__)
        self._cleanup_task = None

    def register_cache(self, cache: AdvancedCache) -> None:
        """Registrar cache para limpeza automática."""
        self.caches.append(cache)

    async def start_cleanup_task(self) -> None:
        """Iniciar tarefa de limpeza automática."""
        if self._cleanup_task is None:
            self._cleanup_task = asyncio.create_task(self._cleanup_loop())

    async def stop_cleanup_task(self) -> None:
        """Parar tarefa de limpeza automática."""
        if self._cleanup_task:
            self._cleanup_task.cancel()
            try:
                await self._cleanup_task
            except asyncio.CancelledError:
                pass
            self._cleanup_task = None

    async def _cleanup_loop(self) -> None:
        """Loop de limpeza automática."""
        while True:
            try:
                await asyncio.sleep(self.cleanup_interval)
                total_cleaned = 0
                for cache in self.caches:
                    cleaned = cache.clear_expired()
                    total_cleaned += cleaned

                if total_cleaned > 0:
                    self.logger.info(f"Cache cleanup: removidas {total_cleaned} entradas expiradas")

            except asyncio.CancelledError:
                break
            except Exception as exc:
                self.logger.error(f"Erro na limpeza de cache: {exc}")


# Instância global do gerenciador de cache
cache_manager = CacheManager()


class OKXOrder:
    """Classe para gerenciar operações de ordens na exchange OKX com estratégia Trailing Stop."""

    _instance = None

    def __new__(cls, client: OKXClient = None):
        if cls._instance is None:
            cls._instance = super().__new__(cls)
            cls._instance._initialized = False
        return cls._instance

    def __init__(self, client: OKXClient = None):
        if getattr(self, "_initialized", False):
            return
        self._initialized = True
        self.client = client or OKXClient()
        self.logger = TradingLogger.get_logger(__name__)
        self.validator = OrderValidator(self.client)

        # Garantir que o cache de ordens esteja inicializado no cliente
        if not hasattr(self.client, "order_cache"):
            self.client.order_cache = OrderCache(cache_duration=30)

        # Inicializar componentes otimizados
        self.cache = AdvancedCache(default_ttl=300, max_size=1000)
        self.volatility_calculator = VolatilityCalculator(
            lookback_periods=getattr(client.config, 'ATR_LOOKBACK_PERIODS', 20),
            cache_ttl=300
        )
        self.position_sizer = PositionSizer(client.config)
        self.trailing_calculator = TrailingStopCalculator(client.config)

        # Registrar caches para limpeza automática
        cache_manager.register_cache(self.cache)
        cache_manager.register_cache(self.volatility_calculator.cache)

    def validate_order_conditions(self, symbol: str) -> bool:
        """
        Valida se as condições estão adequadas para colocar uma ordem.

        Args:
            symbol: Par de trading

        Returns:
            True se condições estão OK, False caso contrário
        """
        return self.validator.validate_order_conditions(symbol, strategy_type="trailing")

    def _generate_client_order_id(self) -> str:
        return uuid.uuid4().hex[:32]

    def has_active_trailing_stop(self, symbol: str) -> bool:
        """
        Verifica especificamente se há ordens trailing stop ativas.

        Args:
            symbol: Par de trading

        Returns:
            True se há ordens trailing stop ativas, False caso contrário
        """
        try:
            # Verificar primeiro no cache
            open_orders = self.client.order_cache.get_open_orders(symbol)
            if open_orders is not None:
                trailing_orders = [
                    order
                    for order in open_orders
                    if "move_order_stop" in order.get("info", {}).get("ordType", "")
                ]
                active_count = len(trailing_orders)
                if active_count > 0:
                    return True
                return False

            # Se não estiver no cache, buscar da exchange
            trailing_orders = check_trailing_stop_orders(self.client, symbol)
            active_count = len(trailing_orders)
            if active_count > 0:
                for order in trailing_orders:
                    order_id = order.get("id", "N/A")
                    status = order.get("status", "N/A")
                    self.logger.debug(
                        "Trailing stop ativo: ID=%s, Status=%s", order_id, status
                    )
                return True
            return False
        except Exception as exc:
            self.logger.debug("Erro ao buscar dados para %s: %s", symbol, str(exc))
            self.logger.info("O símbolo %s não existe na exchange.", symbol)
            return True  # Em caso de erro, assume que há ordens ativas para ser cauteloso

    async def wait_for_order_execution(
        self, order_id: str, symbol: str, timeout: int = 60, max_retries: int = 3
    ) -> Optional[Dict]:
        start_time = time.time()
        retries = 0
        while time.time() - start_time < timeout:
            try:
                order = self.client.exchange.fetch_order(order_id, symbol)
                if order["status"] == "closed":
                    self.logger.info(f"Ordem {order_id} executada com sucesso.")
                    return order
                elif order["status"] == "canceled":
                    self.logger.warning(f"Ordem {order_id} foi cancelada.")
                    return None
                await asyncio.sleep(2)  # Aumentar o intervalo entre tentativas
            except Exception as exc:
                retries += 1
                self.logger.error(
                    f"Erro ao verificar status da ordem {order_id} (Tentativa {retries}/{max_retries}): {exc}"
                )
                if retries >= max_retries:
                    self.logger.error(
                        f"Limite de tentativas atingido para a ordem {order_id}. Abortando."
                    )
                    return None
                await asyncio.sleep(3)  # Aguardar mais tempo antes de nova tentativa
        self.logger.warning(
            f"Tempo esgotado ao esperar pela execução da ordem {order_id}."
        )
        return None

    @performance_monitor
    async def place_buy_order_with_trailing_stop(
        self, symbol: str, indicator: TechnicalIndicator, timeframe: str = "15m"
    ) -> Optional[Dict]:
        try:
            # Verificar cache de ordens ativas primeiro (cache mais agressivo)
            cache_key = f"active_orders_{symbol}"
            cached_orders = self.cache.get(cache_key, ttl=15)  # TTL reduzido para 15s

            if cached_orders is None:
                if check_trailing_stop_orders(self.client, symbol):
                    self.cache.set(cache_key, True, ttl=60)  # Cache por mais tempo se há ordens
                    self.logger.info("Já existem ordens abertas para %s, pulando criação", symbol)
                    return None
                self.cache.set(cache_key, False, ttl=15)  # Cache negativo por menos tempo
            elif cached_orders:
                self.logger.debug("Ordens ativas em cache para %s, pulando criação", symbol)
                return None

            # Obter preço com cache mais agressivo
            price_cache_key = f"best_bid_{symbol}"
            entry_price = self.cache.get(price_cache_key, ttl=5)  # TTL reduzido para 5s

            if entry_price is None:
                entry_price = self.client.get_best_bid(symbol)
                if not entry_price:
                    self.logger.error("Não foi possível obter melhor bid para %s", symbol)
                    return None
                self.cache.set(price_cache_key, entry_price, ttl=5)

            self.logger.debug("Melhor bid obtido para %s: %s", symbol, entry_price)

            # Obter saldo com cache mais longo
            balance_cache_key = "account_balance"
            balance = self.cache.get(balance_cache_key, ttl=60)  # TTL aumentado para 60s

            if balance is None:
                balance = self.client.get_balance()
                self.cache.set(balance_cache_key, balance, ttl=60)

            quote_currency = symbol.split("/")[1]
            available_balance = balance["total"].get(quote_currency, 0)
            if available_balance <= 0:
                self.logger.error("Saldo insuficiente em %s para %s", quote_currency, symbol)
                return None
            # Calcular métricas de volatilidade usando o calculador otimizado
            volatility_metrics = self.volatility_calculator.calculate_volatility_metrics(
                symbol, indicator, timeframe
            )

            # Calcular tamanho da posição usando o calculador otimizado
            position_size = self.position_sizer.calculate_position_size(
                symbol, available_balance, volatility_metrics
            )

            balance_to_use = available_balance * position_size
            amount = balance_to_use / entry_price
            formatted_amount = self.client.format_amount_with_precision(symbol, amount)
            formatted_price = self.client.format_price_with_precision(
                symbol, entry_price
            )
            if float(formatted_amount) <= 0:
                self.logger.error(
                    "Quantidade calculada insuficiente para %s: %s",
                    symbol,
                    formatted_amount,
                )
                return None
            self.logger.debug(
                "Calculado para %s - Quantidade: %s, Preço: %s",
                symbol,
                formatted_amount,
                formatted_price,
            )

            limit_order = self.client.exchange.create_order(
                symbol=symbol,
                type="limit",
                side="buy",
                amount=float(formatted_amount),
                price=float(formatted_price),
                params={"tdMode": "cash", "clOrdId": self._generate_client_order_id()},
            )
            if "amount" not in limit_order or limit_order["amount"] is None:
                limit_order["amount"] = float(formatted_amount)
            self.logger.info(
                "Ordem limite criada com sucesso: %s", limit_order.get("id")
            )

            # Aguardar execução da ordem limite
            executed_order = await self.wait_for_order_execution(
                limit_order["id"], symbol
            )
            if not executed_order:
                self.logger.error(
                    f"Ordem de compra {limit_order['id']} não foi executada."
                )
                return None

            entry_price_real = (
                executed_order["average"]
                if "average" in executed_order
                else executed_order["price"]
            )

            # Calcular ATR para trailing stop
            atr = None
            if volatility_metrics:
                atr = volatility_metrics.get('current_atr')

            if atr is None:
                atr = indicator.calculate_atr(symbol, timeframe, period=14)
                if atr is None:
                    self.logger.error("Não foi possível calcular ATR para %s", symbol)
                    return None

            # Calcular callback ratio usando o calculador otimizado
            callback_ratio = self.trailing_calculator.calculate_callback_ratio(
                symbol, entry_price_real, atr, volatility_metrics
            )

            trailing_params = {
                "instId": self.client.exchange.markets[symbol]["id"],
                "tdMode": "cash",
                "side": "sell",
                "ordType": "move_order_stop",
                "sz": formatted_amount,
                "callbackRatio": str(callback_ratio),
                "activePx": "",
                "clOrdId": self._generate_client_order_id(),
            }

            trailing_order = self.client.exchange.private_post_trade_order_algo(
                trailing_params
            )

            if trailing_order and trailing_order.get("code") == "0":
                algo_id = trailing_order.get("data", [{}])[0].get("algoId", "N/A")
                self.logger.info("Trailing stop criado com sucesso: %s", algo_id)
                # Salvar o trailing stop no banco de dados
                trailing_stop_data = {
                    "algo_id": algo_id,
                    "order_id": limit_order.get("id"),
                    "symbol": symbol,
                    "callback_ratio": callback_ratio,
                    "status": "active",
                }

                bot = TradingBot()  # Instância do bot para acessar o banco de dados
                bot.db.save_trailing_stop(trailing_stop_data)
            else:
                self.logger.error("Falha ao criar trailing stop: %s", trailing_order)

            result = {
                "limit_order": limit_order,
                "entry_price": entry_price_real,
                "amount": float(formatted_amount),
                "symbol": symbol,
            }
            self.logger.info("Ordem com Trailing Stop processada com sucesso para %s", symbol)
            return result

        except Exception as exc:
            self.logger.error("Erro ao colocar ordem para %s: %s", symbol, str(exc))
            return None

    def log_order_prevention_details(self, symbol: str) -> None:
        try:
            trailing_orders = check_trailing_stop_orders(self.client, symbol)
            if trailing_orders:
                self.logger.info("=== PREVENÇÃO DE ORDEM ATIVADA ===")
                self.logger.info("Símbolo: %s", symbol)
                self.logger.info("Trailing stops ativos: %d", len(trailing_orders))
                for i, order in enumerate(trailing_orders, 1):
                    self.logger.info("Trailing Stop #%d:", i)
                    self.logger.info("  - ID: %s", order.get("id", "N/A"))
                    self.logger.info("  - Status: %s", order.get("status", "N/A"))
                    self.logger.info("  - Lado: %s", order.get("side", "N/A"))
                    self.logger.info("  - Quantidade: %s", order.get("amount", "N/A"))
                    self.logger.info(
                        "  - Trigger: %s", order.get("triggerPrice", "N/A")
                    )
                self.logger.info("Nova ordem BLOQUEADA para %s", symbol)
                self.logger.info("=====================================")
        except Exception as exc:
            self.logger.error("Erro ao registrar detalhes de prevenção: %s", str(exc))


class TrailingTradingBot(TradingBotBase):
    """Bot de trading específico para estratégia Trailing Stop WMA."""

    def __init__(self, config: BotConfig):
        super().__init__(config)
        self.semaphore = asyncio.Semaphore(5)  # Limitar concorrência a 5 símbolos simultâneos
        self.processing_cache = AdvancedCache(default_ttl=60)

    @performance_monitor
    async def process_symbol_with_semaphore(self, semaphore: asyncio.Semaphore, symbol: str,
                                          client: OKXClient, indicator: TechnicalIndicator,
                                          signal_checker: SinaisWmaRsi, order_manager: OKXOrder,
                                          db: TradingDatabase) -> Optional[Dict]:
        """Processar um símbolo com controle de semáforo otimizado."""
        async with semaphore:
            try:
                # Verificar cache de processamento recente (mais agressivo)
                cache_key = f"processed_{symbol}"
                cached_result = self.processing_cache.get(cache_key)
                if cached_result is not None:
                    self.logger.debug(f"Símbolo {symbol} processado recentemente, pulando")
                    return None

                # Verificar se há ordens ativas primeiro (operação mais rápida)
                active_orders_cache_key = f"active_check_{symbol}"
                has_active = self.processing_cache.get(active_orders_cache_key)

                if has_active is None:
                    has_active = order_manager.has_active_trailing_stop(symbol)
                    self.processing_cache.set(active_orders_cache_key, has_active, ttl=15)

                if has_active:
                    self.processing_cache.set(cache_key, "has_active", ttl=30)
                    return None

                # Check for closed trailing stop orders (em background se necessário)
                try:
                    closed_orders = check_closed_trailing_stop_orders(client, symbol, db)
                    if closed_orders:
                        print(f"🔄 Processed {len(closed_orders)} closed trailing stop orders for {symbol}")
                except Exception as e:
                    self.logger.warning(f"Erro ao verificar ordens fechadas para {symbol}: {e}")

                # Verificar sinal de entrada (operação mais custosa)
                signal_cache_key = f"signal_{symbol}"
                cached_signal = self.processing_cache.get(signal_cache_key)

                if cached_signal is None:
                    has_buy_signal = signal_checker.check_entry_signal(symbol, timeframe=None)
                    self.processing_cache.set(signal_cache_key, has_buy_signal, ttl=10)
                else:
                    has_buy_signal = cached_signal

                # Print signal apenas se necessário
                if has_buy_signal:
                    signal_checker.print_signal(symbol, timeframe=None)

                if has_buy_signal:
                    self.logger.info(f"\n🚨 SINAL DE COMPRA DETECTADO PARA {symbol}!")
                    if order_manager.validate_order_conditions(symbol):
                        self.logger.info(f"✅ Condições validadas para {symbol}")
                        order_result = await order_manager.place_buy_order_with_trailing_stop(
                            symbol=symbol, indicator=indicator
                        )
                        if order_result:
                            print(f"🎯 Trail Order - ORDEM EXECUTADA COM SUCESSO!")
                            print(f"• Símbolo: {symbol}")
                            print(f"• Quantidade: {order_result['amount']}")
                            print(f"• Preço: ${order_result['entry_price']}")
                            print(f"• ID da ordem: {order_result['limit_order'].get('id', 'N/A')}")

                            self.save_order_to_db(order_result["limit_order"])
                            self.created_orders.append(order_result["limit_order"])
                            await self.play_alert("success", volume=0.7)

                            # Create safe Telegram message without special characters
                            script_name = str(self.config.SCRIPT_NAME).replace('-', ' ').replace('_', '\\_')
                            symbol_safe = str(symbol).replace('-', '\\-').replace('_', '\\_')
                            amount_safe = str(order_result['amount']).replace('-', '\\-')
                            price_safe = str(order_result['entry_price']).replace('-', '\\-')

                            message = f"""🎯 ORDEM TRAILING STOP EXECUTADA

Bot: {script_name}
Símbolo: {symbol_safe}
Tipo: Trailing Stop Limit
Quantidade: {amount_safe}
Preço: {price_safe}
"""
                            await self.send_telegram_notification(message)
                            self.processing_cache.set(cache_key, True, ttl=300)  # Cache por 5 minutos após ordem
                            return order_result
                        else:
                            self.logger.error(f"❌ Falha ao executar ordem para {symbol}")
                            await self.play_alert("error", volume=0.5)
                    else:
                        self.logger.warning(f"⚠️ Condições não atendidas para {symbol}")
                        self.processing_cache.set(cache_key, "conditions_failed", ttl=120)
                else:
                    self.logger.debug(f"Trail Optuna | {symbol} -> Wait")
                    self.processing_cache.set(cache_key, "no_signal", ttl=30)

                return None

            except Exception as exc:
                self.logger.error("Erro ao processar %s: %s", symbol, str(exc))
                print(f"❌ Erro ao processar {symbol}: {exc}")
                return None

    @performance_monitor
    async def _execute_strategy(self, client: OKXClient, indicator: TechnicalIndicator,
                               signal_checker: SinaisWmaRsi, order_manager: OKXOrder, strategy_type: str) -> None:
        """
        Implementação da lógica de trading específica para estratégia Trailing Stop WMA com processamento paralelo.

        Args:
            client: Instance of OKXClient for exchange interactions.
            indicator: TechnicalIndicator instance for calculating indicators.
            signal_checker: SinaisWmaRsi instance for checking entry signals.
            order_manager: OKXOrder instance for managing order placement.
            strategy_type: Type of strategy for notifications.
        """
        print("\n🤖 Verificando Sinais de Trading:")
        print("=" * 50)

        # Initialize database for order tracking
        db = TradingDatabase("trades/sandbox_trades.db")

        # Processar símbolos em paralelo com controle de concorrência
        tasks = []
        for symbol in client.config.TRADING_SYMBOLS:
            task = self.process_symbol_with_semaphore(
                self.semaphore, symbol, client, indicator, signal_checker, order_manager, db
            )
            tasks.append(task)

        # Executar todas as tarefas em paralelo
        results = await asyncio.gather(*tasks, return_exceptions=True)

        # Processar resultados
        successful_orders = 0
        errors = 0
        for i, result in enumerate(results):
            symbol = client.config.TRADING_SYMBOLS[i]
            if isinstance(result, Exception):
                self.logger.error(f"Erro ao processar {symbol}: {result}")
                errors += 1
            elif result is not None:
                successful_orders += 1

        if successful_orders > 0:
            self.logger.info(f"✅ {successful_orders} ordens executadas com sucesso")
        if errors > 0:
            self.logger.warning(f"⚠️ {errors} erros durante o processamento")


class TradingBot(TrailingTradingBot):
    """Classe para compatibilidade com chamadas internas que esperam TradingBot."""
    pass


class OptimizedOptuna:
    """Classe para otimização Optuna com melhorias de performance."""

    def __init__(self, client: OKXClient, indicator: TechnicalIndicator):
        self.client = client
        self.indicator = indicator
        self.logger = TradingLogger.get_logger(__name__)
        self.cached_data = {}
        self.preload_data()

    def preload_data(self) -> None:
        """Pré-carregar dados históricos para todos os símbolos."""
        self.logger.info("Pré-carregando dados históricos para otimização...")
        for symbol in self.client.config.TRADING_SYMBOLS:
            try:
                # Use the correct method signature - fetch_historical_data uses days parameter
                ohlcv_data = self.indicator.fetch_historical_data(symbol, "15m", days=7)
                if ohlcv_data is not None and len(ohlcv_data) > 100:
                    df = pd.DataFrame(ohlcv_data, columns=["timestamp", "open", "high", "low", "close", "volume"])
                    self.cached_data[symbol] = df
                    self.logger.debug(f"Dados carregados para {symbol}: {len(df)} candles")
            except Exception as exc:
                self.logger.warning(f"Erro ao carregar dados para {symbol}: {exc}")

    def calculate_strategy_performance(self, trial_params: Dict[str, Any]) -> float:
        """Calcular performance da estratégia com parâmetros do trial."""
        total_return = 0.0
        total_trades = 0
        winning_trades = 0

        for symbol, df in self.cached_data.items():
            if len(df) < trial_params["wma_period"] + 14:
                continue

            # Calcular indicadores
            df_copy = df.copy()
            df_copy["wma"] = talib.WMA(df_copy["close"], timeperiod=trial_params["wma_period"])
            df_copy["rsi"] = talib.RSI(df_copy["close"], timeperiod=14)
            df_copy["atr"] = talib.ATR(df_copy["high"], df_copy["low"], df_copy["close"], timeperiod=14)

            # Simular trading
            position = 0
            entry_price = 0.0
            symbol_returns = []

            for i in range(trial_params["wma_period"] + 14, len(df_copy)):
                current_row = df_copy.iloc[i]

                # Condições de entrada
                if (position == 0 and
                    current_row["rsi"] < trial_params["rsi_low"] and
                    current_row["close"] > current_row["wma"] and
                    not pd.isna(current_row["atr"])):

                    position = 1
                    entry_price = current_row["close"]

                # Condições de saída
                elif position == 1:
                    callback_ratio = trial_params["atr_multiplier"] * current_row["atr"] / entry_price
                    stop_price = entry_price * (1 + callback_ratio)

                    exit_price = None

                    # Trailing stop ativado
                    if current_row["high"] >= stop_price:
                        exit_price = stop_price
                    # RSI alto - saída por indicador
                    elif current_row["rsi"] > trial_params["rsi_high"]:
                        exit_price = current_row["close"]

                    if exit_price:
                        trade_return = (exit_price - entry_price) / entry_price
                        symbol_returns.append(trade_return)
                        total_trades += 1
                        if trade_return > 0:
                            winning_trades += 1
                        position = 0

            if symbol_returns:
                symbol_performance = np.mean(symbol_returns) * len(symbol_returns)
                total_return += symbol_performance

        # Calcular métricas de qualidade
        win_rate = winning_trades / total_trades if total_trades > 0 else 0

        # Penalizar estratégias com poucos trades ou baixa win rate
        if total_trades < 10:
            return -1.0
        if win_rate < 0.3:
            return -0.5

        # Função objetivo multi-critério
        return total_return * 0.7 + win_rate * 0.3

    def create_objective_function(self):
        """Criar função objetivo otimizada para Optuna."""
        def objective(trial: optuna.Trial) -> float:
            # Early stopping se muitos trials ruins
            if trial.number > 50:
                study_trials = trial.study.trials
                recent_trials = study_trials[-20:]  # Últimos 20 trials
                recent_values = [t.value for t in recent_trials if t.value is not None]
                if recent_values and np.mean(recent_values) < -0.5:
                    raise optuna.exceptions.TrialPruned()

            # Definir parâmetros com ranges otimizados
            trial_params = {
                "wma_period": trial.suggest_int("wma_period", 30, 80),
                "rsi_low": trial.suggest_int("rsi_low", 20, 40),
                "rsi_high": trial.suggest_int("rsi_high", 60, 80),
                "atr_multiplier": trial.suggest_float("atr_multiplier", 1.5, 3.5),
                "volatility_threshold_high": trial.suggest_float("volatility_threshold_high", 1.1, 1.4),
                "volatility_threshold_low": trial.suggest_float("volatility_threshold_low", 0.6, 0.9),
                "position_size_min": trial.suggest_float("position_size_min", 0.03, 0.07),
                "position_size_base": trial.suggest_float("position_size_base", 0.08, 0.12),
                "position_size_max": trial.suggest_float("position_size_max", 0.13, 0.18)
            }

            # Validações de parâmetros
            if trial_params["rsi_low"] >= trial_params["rsi_high"]:
                return -1.0
            if trial_params["position_size_min"] >= trial_params["position_size_base"]:
                return -1.0
            if trial_params["position_size_base"] >= trial_params["position_size_max"]:
                return -1.0

            performance = self.calculate_strategy_performance(trial_params)

            # Implementar pruning baseado em performance intermediária
            if trial.number > 10 and performance < -0.3:
                raise optuna.exceptions.TrialPruned()

            return performance

        return objective


@performance_monitor
async def optimize_parameters(client: OKXClient, indicator: TechnicalIndicator, timeframe: str = "15m") -> Dict[str, Any]:
    """Função otimizada para otimizar parâmetros da estratégia usando Optuna."""
    logger = TradingLogger.get_logger(__name__)
    logger.info("Iniciando otimização de parâmetros com Optuna...")

    # Criar otimizador
    optimizer = OptimizedOptuna(client, indicator)
    objective_func = optimizer.create_objective_function()

    # Configurar estudo com pruning
    pruner = optuna.pruners.MedianPruner(n_startup_trials=20, n_warmup_steps=10)
    sampler = optuna.samplers.TPESampler(n_startup_trials=30)

    study = optuna.create_study(
        direction="maximize",
        storage="sqlite:///optuna_studies.db",
        study_name="trail_wma_optimization_v2",
        load_if_exists=True,
        pruner=pruner,
        sampler=sampler
    )

    # Otimizar com timeout e número máximo de trials
    study.optimize(objective_func, n_trials=200, timeout=1800)  # 30 minutos

    # Obter melhores parâmetros
    best_params = study.best_params
    logger.info("Otimização concluída. Melhores parâmetros encontrados: %s", best_params)
    logger.info("Melhor valor: %.4f", study.best_value)
    logger.info("Número de trials completados: %d", len(study.trials))

    return best_params

@performance_monitor
async def main() -> None:
    """Função principal otimizada com auto-trading para estratégia Trailing Stop WMA com otimização Optuna."""
    logger = TradingLogger.get_logger(__name__)

    try:
        config = BotConfig(strategy="signals_wma_rsi")
        config.SCRIPT_NAME = "999 Base Trail WMA Optuna - Optimized"
        client = OKXClient(config)
        # Ensure TRADING_SYMBOLS from config are used in client
        client.config.TRADING_SYMBOLS = config.TRADING_SYMBOLS

        # Inicializar componentes otimizados
        order_manager = OKXOrder(client)
        bot = TradingBot(config)
        indicator = TechnicalIndicator(client)

        await send_startup_notification(client, bot)
        await bot.play_alert("start", volume=0.25)

        # Pré-carregar dados históricos em paralelo
        logger.info("Pré-carregando dados históricos...")
        preload_tasks = []
        for symbol in client.config.TRADING_SYMBOLS:
            task = asyncio.create_task(
                asyncio.to_thread(indicator.fetch_historical_data, symbol, client.config.TIMEFRAME, 7)
            )
            preload_tasks.append(task)

        await asyncio.gather(*preload_tasks, return_exceptions=True)
        logger.info("Dados históricos carregados com sucesso")

        # Otimizar parâmetros usando Optuna em background se necessário
        optimization_needed = True  # Pode ser configurado baseado em última otimização

        if optimization_needed:
            logger.info("Iniciando otimização de parâmetros...")
            best_params = await optimize_parameters(client, indicator, client.config.TIMEFRAME)

            # Aplicar os melhores parâmetros à configuração
            client.config.WMA_PERIOD = best_params.get("wma_period", 50)
            client.config.RSI_LOW = best_params.get("rsi_low", 30)
            client.config.RSI_HIGH = best_params.get("rsi_high", 70)
            client.config.ATR_MULTIPLIER = best_params.get("atr_multiplier", 1.75)
            client.config.VOLATILITY_THRESHOLD_HIGH = best_params.get("volatility_threshold_high", 1.2)
            client.config.VOLATILITY_THRESHOLD_LOW = best_params.get("volatility_threshold_low", 0.8)
            client.config.POSITION_SIZE_MIN = best_params.get("position_size_min", 0.05)
            client.config.POSITION_SIZE_BASE = best_params.get("position_size_base", 0.1)
            client.config.POSITION_SIZE_MAX = best_params.get("position_size_max", 0.15)
            logger.info("Parâmetros otimizados aplicados à configuração do bot.")
        else:
            logger.info("Usando parâmetros padrão - otimização pulada")

        # Inicializar verificador de sinais
        signal_checker = SinaisWmaRsi(indicator, client)

        # Iniciar gerenciador de cache
        await cache_manager.start_cleanup_task()
        logger.info("Gerenciador de cache iniciado")

        # Iniciar loop de trading otimizado
        logger.info("Iniciando loop de trading otimizado...")
        try:
            await bot.run_trading_loop(client, indicator, signal_checker, order_manager, "TRAIL")
        finally:
            # Parar gerenciador de cache
            await cache_manager.stop_cleanup_task()
            logger.info("Gerenciador de cache finalizado")

    except KeyboardInterrupt:
        logger.info("Bot interrompido manualmente pelo usuário.")
        print("\n🛑 Bot interrompido manualmente.")
        if "bot" in locals():
            await bot.play_alert("exit", volume=0.9)
        print("A finalizar o bot. Até breve!")
        return

    except (ConfigurationError, ExchangeConnectionError) as exc:
        logger.error("Erro de configuração/conexão: %s", exc)
        print(f"❌ Erro: {exc}")
        print("Por favor, verifique as suas credenciais e configuração.")

    except Exception as exc:
        logger.error("Erro inesperado: %s", exc)
        logger.exception(exc)
        print(f"❌ Erro inesperado: {exc}")
        print("Verifique os logs para mais detalhes.")

        # Tentar limpeza de recursos em caso de erro
        try:
            if "order_manager" in locals() and hasattr(order_manager, "cache"):
                order_manager.cache.clear_expired()
        except Exception:
            pass


if __name__ == "__main__":
    asyncio.run(main())
