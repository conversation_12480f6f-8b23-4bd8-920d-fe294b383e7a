"""
Centralized configuration handling for trading bots to reduce code duplication.
This module contains the BotConfig class and OrderCache for managing configuration and caching orders.
"""

import os
import json
import time
from dataclasses import dataclass
from typing import Dict, List, Optional, Any
from utils.common import load_parameters


@dataclass
class BotConfig:
    """Centralized configuration for trading bots."""
    
    strategy: str = "OCO_wma_rsi"
    SANDBOX_MODE: bool = True
    TRADING_SYMBOLS: List[str] = None
    TIMEFRAME: str = "15m"
    RSI_PERIOD: int = 14
    WMA_PERIOD: int = 50
    OHLCV_LIMIT: int = 100
    ATR_SL_FACTOR: float = 2.5
    ATR_TP_FACTOR: float = 3.5
    SL_MULTIPLIER: float = 1.55
    ATR_MULTIPLIER: float = 1.5
    ENABLE_DYNAMIC_POSITION_SIZING: bool = True
    ENABLE_DYNAMIC_ATR_MULTIPLIERS: bool = True
    ENABLE_DYNAMIC_TRAILING_RATIO: bool = True
    POSITION_SIZE_BASE: float = 0.1
    POSITION_SIZE_MIN: float = 0.05
    POSITION_SIZE_MAX: float = 0.15
    ATR_SL_FACTOR_MIN: float = 2.0
    ATR_SL_FACTOR_MAX: float = 4.0
    ATR_TP_FACTOR_MIN: float = 3.0
    ATR_TP_FACTOR_MAX: float = 5.0
    CALLBACK_RATIO_BASE: float = 0.03
    CALLBACK_RATIO_MIN: float = 0.015
    CALLBACK_RATIO_MAX: float = 0.08
    ATR_LOOKBACK_PERIODS: int = 20
    VOLATILITY_THRESHOLD_HIGH: float = 1.2
    VOLATILITY_THRESHOLD_LOW: float = 0.8
    STABLECOINS: List[str] = None
    FIAT_DECIMAL_PLACES: int = 2
    CRYPTO_DECIMAL_PLACES: int = 8
    ENABLE_SOUND_NOTIFICATIONS: bool = True
    ENABLE_TELEGRAM_NOTIFICATIONS: bool = True
    SWING_BARS: int = 3
    SWING_BARS2: int = 3
    CLUSTER_BARS: int = 20
    NUM_CLUSTERS: int = 3
    ADX_PERIOD: int = 14

    def __post_init__(self):
        """Initialize default values after creation."""
        self.SCRIPT_NAME = os.path.splitext(os.path.basename(__file__))[0].replace("_", " ")
        if self.TRADING_SYMBOLS is None:
            self.TRADING_SYMBOLS = []
        if self.STABLECOINS is None:
            self.STABLECOINS = ["USDC", "USDT", "BUSD", "FDUSD"]
        self._initialize_defaults()
        self._load_strategy_parameters()

    def _initialize_defaults(self):
        """Initialize default values from OKXClient if not set."""
        try:
            from core.okx_client import OKXClient
            default_config = OKXClient().config
            default_attrs = [attr for attr in dir(default_config) if attr.isupper()]
            for attr in default_attrs:
                if (not hasattr(self, attr) or getattr(self, attr) is None or
                    (isinstance(getattr(self, attr), (int, float)) and getattr(self, attr) == 0)):
                    default_val = getattr(default_config, attr, None)
                    if default_val is not None:
                        setattr(self, attr, default_val)
        except ImportError:
            pass  # If OKXClient is not available yet, skip initialization

    def _load_strategy_parameters(self):
        """Load parameters specific to the selected strategy from parameters.json."""
        params = load_parameters()
        if self.strategy in params:
            strategy_params = params[self.strategy]
            for key, value in strategy_params.items():
                if hasattr(self, key):
                    setattr(self, key, value)


class OrderCache:
    """Local cache for orders to reduce API calls to the exchange."""

    def __init__(self, cache_duration: int = 30):
        """
        Initialize the order cache.
        
        Args:
            cache_duration: Duration in seconds before refreshing the cache.
        """
        self.cache_duration = cache_duration
        self.open_orders: Dict[str, List[Dict]] = {}
        self.closed_orders: Dict[str, List[Dict]] = {}
        self.last_updated: Dict[str, float] = {}

    def get_open_orders(self, symbol: str) -> Optional[List[Dict]]:
        """
        Get open orders from cache if they are up-to-date.
        
        Args:
            symbol: Trading pair symbol.
        
        Returns:
            List of open orders or None if cache is outdated.
        """
        if symbol in self.open_orders and symbol in self.last_updated:
            if time.time() - self.last_updated[symbol] < self.cache_duration:
                return self.open_orders[symbol]
        return None

    def get_closed_orders(self, symbol: str) -> Optional[List[Dict]]:
        """
        Get closed orders from cache if they are up-to-date.
        
        Args:
            symbol: Trading pair symbol.
        
        Returns:
            List of closed orders or None if cache is outdated.
        """
        if symbol in self.closed_orders and symbol in self.last_updated:
            if time.time() - self.last_updated[symbol] < self.cache_duration:
                return self.closed_orders[symbol]
        return None

    def update_orders(self, symbol: str, open_orders: List[Dict], closed_orders: List[Dict]) -> None:
        """
        Update the cache with new orders.
        
        Args:
            symbol: Trading pair symbol.
            open_orders: List of open orders.
            closed_orders: List of closed orders.
        """
        self.open_orders[symbol] = open_orders
        self.closed_orders[symbol] = closed_orders
        self.last_updated[symbol] = time.time()
