#!/usr/bin/env python3
"""
Teste para validar mensagens do Telegram sem caracteres especiais.
"""

def test_telegram_message_escaping():
    """Testar se a mensagem do Telegram está corretamente formatada."""
    
    # Simular dados de ordem
    config_script_name = "999 Base Trail WMA Optuna - Optimized"
    symbol = "BTC/USDC"
    amount = 0.001
    entry_price = 45000.50
    
    # Aplicar escapes como no código
    script_name = str(config_script_name).replace('-', '\\-').replace('_', '\\_')
    symbol_safe = str(symbol).replace('-', '\\-').replace('_', '\\_')
    amount_safe = str(amount).replace('-', '\\-')
    price_safe = str(entry_price).replace('-', '\\-')
    
    message = f"""🎯 ORDEM TRAILING STOP EXECUTADA

Bot: {script_name}
Símbolo: {symbol_safe}
Tipo: Trailing Stop Limit
Quantidade: {amount_safe}
Preço: {price_safe}
"""
    
    print("Mensagem formatada para Telegram:")
    print("=" * 50)
    print(message)
    print("=" * 50)
    
    # Verificar se há caracteres problemáticos não escapados
    problematic_chars = ['-', '_', '(', ')', '[', ']', '~', '`', '>', '#', '+', '=', '|', '{', '}', '.', '!']
    
    issues = []
    for char in problematic_chars:
        if char in message and f'\\{char}' not in message:
            # Verificar se o caractere não está em contexto seguro
            if char == '-' and '\\-' not in message:
                issues.append(f"Caractere '{char}' não escapado encontrado")
            elif char == '_' and '\\_' not in message:
                issues.append(f"Caractere '{char}' não escapado encontrado")
    
    if issues:
        print("⚠️ Possíveis problemas encontrados:")
        for issue in issues:
            print(f"  - {issue}")
    else:
        print("✅ Mensagem parece estar corretamente formatada")
    
    # Testar caracteres específicos que causaram problemas
    test_cases = [
        ("Hífen simples", "Test-Message", "Test\\-Message"),
        ("Underscore", "Test_Message", "Test\\_Message"),
        ("Parênteses", "Test (Limit)", "Test \\(Limit\\)"),
        ("Múltiplos hífens", "999-Base-Trail", "999\\-Base\\-Trail"),
    ]
    
    print("\n🧪 Testes de casos específicos:")
    for test_name, input_text, expected in test_cases:
        escaped = input_text.replace('-', '\\-').replace('_', '\\_').replace('(', '\\(').replace(')', '\\)')
        status = "✅" if escaped == expected else "❌"
        print(f"{status} {test_name}: '{input_text}' -> '{escaped}'")

if __name__ == "__main__":
    test_telegram_message_escaping()
